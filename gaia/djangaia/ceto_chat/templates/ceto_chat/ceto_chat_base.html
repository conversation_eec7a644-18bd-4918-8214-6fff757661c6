<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ceto Chat</title>

    <!-- Vue.js library -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Load CSS and JavaScript files -->
    {% load static %}
    <link rel="stylesheet" type="text/css" href="{% static 'ceto_chat/ceto_chat.css' %}">
    <script src="{% static 'ceto_chat/debug.js' %}" defer></script>
    <script src="{% static 'ceto_chat/ceto_app.js' %}" defer></script>
</head>
<body>
    <div id="app" class="app-container">
        <!-- Debug Panel Toggle Button -->
        <button @click="toggleDebugPanel"
                class="btn btn-sm debug-toggle"
                :class="debugPanelVisible ? 'btn-warning' : 'btn-outline-secondary'"
                style="position: fixed; top: 10px; right: 10px; z-index: 1050; background: #333; color: white; border: 1px solid #666;">
            <i class="fas fa-bug"></i> Debug
        </button>

        <div class="main-content">
            <h1>Ceto Chat</h1>

            <!-- Level 0032: Test MCP Tool Button -->
            <div class="test-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;">
                <h3>Level 0032: MCP Tool Test</h3>
                <p>Test the echostring MCP tool (temporary - will be removed in higher levels)</p>

                <div style="margin: 10px 0;">
                    <input type="text"
                           v-model="testMessage"
                           placeholder="Enter message to echo..."
                           style="padding: 8px; width: 300px; margin-right: 10px; border: 1px solid #ccc; border-radius: 4px;">
                    <button @click="testEchoTool"
                            :disabled="mcpToolLoading"
                            style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;"
                            :style="{ opacity: mcpToolLoading ? 0.6 : 1 }">
                        <i class="fas fa-play" v-if="!mcpToolLoading"></i>
                        <i class="fas fa-spinner fa-spin" v-if="mcpToolLoading"></i>
                        <span v-if="mcpToolLoading">Calling...</span>
                        <span v-if="!mcpToolLoading">Test Echo Tool</span>
                    </button>
                </div>

                <!-- Response Display -->
                <div v-if="lastToolResponse"
                     style="margin-top: 15px; padding: 10px; background: #e9ecef; border-radius: 4px; border-left: 4px solid #007bff;">
                    <strong>Tool Response:</strong>
                    <pre style="margin: 5px 0 0 0; white-space: pre-wrap; font-family: monospace;">{{ lastToolResponse }}</pre>
                </div>
            </div>
        </div>

        <!-- Debug Panel -->
        <div v-if="debugPanelVisible" class="debug-panel">
            <div class="debug-header">
                <h5>Debug Console</h5>
                <div class="debug-controls">
                    <button @click="resetAllState"
                            class="btn btn-sm btn-outline-warning"
                            title="Reset all debug state and re-initialize">
                        <i class="fas fa-redo"></i> RESET
                    </button>
                    <button @click="clearDebugLogs" class="btn btn-sm btn-outline-danger">Clear</button>
                    <button @click="toggleDebugPanel" class="btn btn-sm btn-outline-secondary">×</button>
                </div>
            </div>

            <!-- Debug Pane Mode Tabs -->
            <div class="debug-tabs">
                <button @click="setDebugPaneMode('calls')"
                        class="debug-tab"
                        :class="{'active': debugPaneMode === 'calls'}">
                    Call Details
                </button>
                <button @click="setDebugPaneMode('api')"
                        class="debug-tab"
                        :class="{'active': debugPaneMode === 'api'}">
                    API Communications
                </button>
                <button @click="setDebugPaneMode('history')"
                        class="debug-tab"
                        :class="{'active': debugPaneMode === 'history'}">
                    Conversation History
                </button>
                <button @click="setDebugPaneMode('tools')"
                        class="debug-tab"
                        :class="{'active': debugPaneMode === 'tools'}">
                    Tools Listing
                </button>
                <button @click="setDebugPaneMode('errors')"
                        class="debug-tab"
                        :class="{'active': debugPaneMode === 'errors'}">
                    Errors <span v-if="errorLog.length > 0" class="badge badge-danger">{{ errorLog.length }}</span>
                </button>
            </div>

            <!-- Call Details Pane -->
            <div v-if="debugPaneMode === 'calls'" class="debug-content">
                <h6>HTTP Call Details</h6>
                <div v-if="callDetails.length === 0" class="debug-empty">
                    No HTTP calls tracked yet. Send a message to see call details.
                </div>
                <div v-else>
                    <div class="call-details-table">
                        <div class="call-details-header">
                            <span>Time</span>
                            <span>Method</span>
                            <span>URL</span>
                            <span>Status</span>
                            <span>Latency</span>
                            <span>Sent</span>
                            <span>Received</span>
                        </div>
                        <div v-for="call in callDetails"
                             :key="call.id"
                             class="call-details-row"
                             :class="{'call-error': call.status >= 400 || call.status === 'ERROR', 'call-pending': !call.completed}">
                            <span class="call-time" v-text="formatTimestamp(call.timestamp)"></span>
                            <span class="call-method" v-text="call.method"></span>
                            <span class="call-url" v-text="call.url"></span>
                            <span class="call-status" v-text="call.status"></span>
                            <span class="call-latency" v-text="call.latency ? call.latency + ' ms' : (call.completed ? '-' : 'pending')"></span>
                            <span class="call-sent" v-text="formatBytes(call.sentBytes)"></span>
                            <span class="call-received" v-text="formatBytes(call.receivedBytes)"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Communications Pane -->
            <div v-if="debugPaneMode === 'api'" class="debug-content">
                <h6>Raw API Data</h6>
                <div v-if="debugLogs.length === 0" class="debug-empty">
                    No debug logs yet. Send a message to see server communications.
                </div>
                <div v-for="log in debugLogs" :key="log.id" class="debug-log-entry" :class="'debug-' + log.type.toLowerCase()">
                    <div class="debug-log-header">
                        <span class="debug-timestamp" v-text="formatTimestamp(log.timestamp)"></span>
                        <span class="debug-type" :class="'debug-type-' + log.type.toLowerCase()" v-text="log.type"></span>
                        <span class="debug-direction" :class="'debug-direction-' + log.direction.toLowerCase()" v-text="log.direction"></span>
                        <span v-if="log.url" class="debug-url" v-text="log.url"></span>
                    </div>
                    <pre class="debug-log-data" v-text="log.data"></pre>
                </div>
            </div>

            <!-- Conversation History Pane -->
            <div v-if="debugPaneMode === 'history'" class="debug-content">
                <h6>Conversation History</h6>
                <div class="debug-empty">
                    No conversation history available yet.
                </div>
            </div>

            <!-- Tools Listing Pane -->
            <div v-if="debugPaneMode === 'tools'" class="debug-content">
                <h6>MCP Tools</h6>

                <!-- MCP Connection Status -->
                <div class="mcp-status-bar" style="margin-bottom: 10px; padding: 8px; border-radius: 4px;"
                     :style="{ backgroundColor: mcpConnected ? '#d4edda' : '#f8d7da',
                               color: mcpConnected ? '#155724' : '#721c24',
                               border: mcpConnected ? '1px solid #c3e6cb' : '1px solid #f5c6cb' }">
                    <i :class="mcpConnected ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
                    <span v-if="mcpLoading">Loading MCP tools...</span>
                    <span v-else-if="mcpConnected">Connected to {{ mcpServerUrl }} ({{ mcpTools.length }} tools)</span>
                    <span v-else>Not connected to MCP server</span>
                    <button @click="loadMcpTools"
                            class="btn btn-sm btn-outline-secondary"
                            style="float: right; margin-top: -2px;"
                            :disabled="mcpLoading">
                        <i class="fas fa-sync-alt" :class="{ 'fa-spin': mcpLoading }"></i> Refresh
                    </button>
                </div>

                <!-- Tools List -->
                <div v-if="mcpTools.length === 0 && !mcpLoading" class="debug-empty">
                    No MCP tools available. Check server connection.
                </div>
                <div v-else-if="mcpTools.length > 0" class="mcp-tools-list">
                    <div v-for="tool in mcpTools" :key="tool.name" class="mcp-tool-item"
                         style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;">
                        <div class="tool-header" style="font-weight: bold; color: #333;">
                            <i class="fas fa-wrench" style="margin-right: 5px; color: #666;"></i>
                            {{ tool.name }}
                        </div>
                        <div class="tool-description" style="font-size: 0.9em; color: #666; margin-top: 4px;">
                            <span v-text="tool.description || 'No description available'"></span>
                        </div>
                        <div v-if="tool.input_schema && Object.keys(tool.input_schema).length > 0"
                             class="tool-schema" style="font-size: 0.8em; color: #888; margin-top: 4px;">
                            <details>
                                <summary style="cursor: pointer;">Parameters</summary>
                                <pre style="margin-top: 4px; font-size: 0.75em; background: #fff; padding: 4px; border-radius: 2px;" v-text="JSON.stringify(tool.input_schema, null, 2)"></pre>
                            </details>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Errors Pane -->
            <div v-if="debugPaneMode === 'errors'" class="debug-content">
                <h6>Error Log</h6>
                <div v-if="errorLog.length === 0" class="debug-empty">
                    No errors logged yet.
                </div>
                <div v-for="error in errorLog" :key="error.id" class="debug-log-entry debug-error">
                    <div class="debug-log-header">
                        <span class="debug-timestamp" v-text="formatTimestamp(error.timestamp)"></span>
                        <span class="debug-type debug-type-error">ERROR</span>
                        <span v-if="error.source" class="debug-url" v-text="error.source"></span>
                    </div>
                    <pre class="debug-log-data error-text" v-text="error.message"></pre>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
