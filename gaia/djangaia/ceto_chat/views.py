from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import logging

# Import simplified MCP client from api_mcp module
try:
    from .api_mcp import MC<PERSON><PERSON><PERSON>lient, MCP_AVAILABLE
except ImportError:
    MCP_AVAILABLE = False

logger = logging.getLogger(__name__)


def ceto_chat_view(request):
    """
    Renders the Ceto Chat base template.
    """
    return render(request, 'ceto_chat/ceto_chat_base.html')


@csrf_exempt
@require_http_methods(["GET"])
async def list_mcp_tools(request):
    """
    List available MCP tools from the running server.
    Level 0031: Connect to MCP server (async version with dependency injection)
    """
    if not MCP_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'MCP client library not available',
            'tools': []
        }, status=500)

    try:
        # Use the simplified MCP client from api_mcp
        mcp_client = MCPAPIClient(server_name='default')
        result = await mcp_client.get_tools()

        logger.info(f"MCP tools request result: success={result['success']}, "
                   f"tool_count={result.get('tool_count', 0)}")

        if result['success']:
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=500)

    except Exception as e:
        logger.error(f"Error listing MCP tools: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e),
            'tools': []
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
async def call_mcp_tool(request):
    """
    Call an MCP tool with parameters.
    Level 0032: Call MCP tools from frontend
    """
    if not MCP_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'MCP client library not available'
        }, status=500)

    try:
        import json
        data = json.loads(request.body)
        tool_name = data.get('tool_name')
        parameters = data.get('parameters', {})

        if not tool_name:
            return JsonResponse({
                'success': False,
                'error': 'tool_name is required'
            }, status=400)

        logger.info(f"Calling MCP tool '{tool_name}' with parameters: {parameters}")

        # Use the simplified MCP client from api_mcp
        mcp_client = MCPAPIClient(server_name='default')
        result = await mcp_client.call_tool(tool_name, parameters)

        logger.info(f"MCP tool call result: success={result['success']}")

        if result['success']:
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=500)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON in request body'
        }, status=400)
    except Exception as e:
        logger.error(f"Error calling MCP tool: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
